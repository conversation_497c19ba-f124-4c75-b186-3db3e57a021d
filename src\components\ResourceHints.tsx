import React, { useEffect } from 'react';

/**
 * ResourceHints component to improve loading performance by providing resource hints to the browser
 * This component should be included in the App component
 */
const ResourceHints: React.FC = () => {
  useEffect(() => {
    // Check if we're in a browser environment
    if (typeof window === 'undefined' || typeof document === 'undefined') {
      return;
    }

    try {
      // Add preconnect for external domains with proper crossorigin
      addPreconnect([
        { url: 'https://fonts.googleapis.com', crossorigin: true },
        { url: 'https://fonts.gstatic.com', crossorigin: true },
        { url: 'https://cdn.vercel-insights.com', crossorigin: false }
      ]);

      // Add DNS prefetch for less critical domains
      addDnsPrefetch([
        'https://vercel.com'
      ]);

      // Load Google Fonts CSS with proper preload
      loadGoogleFonts();

      // Note: CSS preloading removed to prevent unused preload warnings
      // Vite handles CSS loading automatically and efficiently
    } catch (error) {
      console.warn('ResourceHints: Error setting up resource hints:', error);
    }
  }, []);

  return null; // This component doesn't render anything
};

function addPreconnect(configs: Array<{ url: string; crossorigin: boolean }>) {
  configs.forEach(({ url, crossorigin }) => {
    try {
      if (!document.querySelector(`link[rel="preconnect"][href="${url}"]`)) {
        const link = document.createElement('link');
        link.rel = 'preconnect';
        link.href = url;
        if (crossorigin) {
          link.crossOrigin = 'anonymous';
        }
        document.head.appendChild(link);
      }
    } catch (error) {
      console.warn(`Failed to add preconnect for ${url}:`, error);
    }
  });
}

function addDnsPrefetch(urls: string[]) {
  urls.forEach(url => {
    try {
      if (!document.querySelector(`link[rel="dns-prefetch"][href="${url}"]`)) {
        const link = document.createElement('link');
        link.rel = 'dns-prefetch';
        link.href = url;
        document.head.appendChild(link);
      }
    } catch (error) {
      console.warn(`Failed to add DNS prefetch for ${url}:`, error);
    }
  });
}

function loadGoogleFonts() {
  try {
    // Fonts are now loaded via @import in CSS - this function provides additional optimization
    // Only add preconnect hints for performance, no manual font file preloading

    // Ensure font-display: swap is applied for better performance
    if (!document.querySelector('style[data-font-optimization]')) {
      const style = document.createElement('style');
      style.setAttribute('data-font-optimization', 'true');
      style.textContent = `
        /* Ensure all fonts use font-display: swap for better performance */
        @font-face {
          font-family: 'Inter';
          font-display: swap;
        }
        @font-face {
          font-family: 'Poppins';
          font-display: swap;
        }

        /* Balanced font weights for section titles - luxury & readable */
        .section-title, h1, h2, h3, h4, h5, h6 {
          font-weight: 800 !important;
          font-variation-settings: 'wght' 800;
          -webkit-text-stroke: 0.4px currentColor !important;
          text-shadow:
            0 0 1px currentColor,
            0 0 3px rgba(255, 255, 255, 0.15) !important;
          letter-spacing: -0.015em;
          -webkit-font-smoothing: antialiased;
          -moz-osx-font-smoothing: grayscale;
          filter: contrast(1.02) brightness(1.01);
        }
      `;
      document.head.appendChild(style);
    }
  } catch (error) {
    console.warn('Failed to optimize font loading:', error);
    // Ensure fallback fonts are available
    try {
      const fallbackStyle = document.createElement('style');
      fallbackStyle.textContent = `
        body {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
        }
        /* Fallback balanced styling for section titles */
        .section-title, h1, h2, h3, h4, h5, h6 {
          font-weight: 800 !important;
          -webkit-text-stroke: 0.3px currentColor !important;
          text-shadow: 0 0 1px currentColor !important;
        }
      `;
      document.head.appendChild(fallbackStyle);
    } catch (fallbackError) {
      console.warn('Failed to add fallback font styles:', fallbackError);
    }
  }
}

export default ResourceHints;
